import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import 'package:x1440/api/isolate/network_isolate.dart';
import 'package:x1440/api/isolate/network_isolate_types.dart';
import 'package:x1440/api/salesforce/salesforce_api.dart';
import 'package:x1440/api/shim_service_api.dart';
import 'package:injectable/injectable.dart';
import 'package:x1440/repositories/storage/local_storage_repository.dart';
import 'package:x1440/use_cases/logging/logging_use_case.dart';

/// Custom HttpClientAdapter that routes all requests through NetworkIsolate
class _IsolateHttpClientAdapter implements HttpClientAdapter {
  final NetworkIsolate _networkIsolate;
  final ApiType _apiType;
  final String _baseUrl;

  _IsolateHttpClientAdapter(this._networkIsolate, this._apiType, this._baseUrl);

  @override
  Future<ResponseBody> fetch(RequestOptions options, Stream<Uint8List>? requestStream, Future<void>? cancelFuture) async {
    try {
      // Extract auth token from headers if present
      String? authToken;
      
      if (options.headers.containsKey('Authorization')) {
        final authHeader = options.headers['Authorization'] as String?;
        if (authHeader != null && authHeader.startsWith('Bearer ')) {
          authToken = authHeader.substring(7); // Remove 'Bearer ' prefix
          print('🔑 IsolateHttpClientAdapter - Found authorization token in headers');
        }
      }
      
      // Process all headers and convert to String values
      final customHeaders = <String, String>{};
      options.headers.forEach((key, value) {
        // Skip Authorization header as it's handled separately
        // Skip content-length header as it should be managed by the HTTP client
        if (key != 'Authorization' && key.toLowerCase() != 'content-length') {
          customHeaders[key] = value.toString();
        }
      });
      
      if (customHeaders.isNotEmpty) {
        print('🔑 IsolateHttpClientAdapter - Forwarding ${customHeaders.length} additional headers: ${customHeaders.keys.join(', ')}');
      }
      
      // Convert Dio request to NetworkIsolate request
      final request = NetworkIsolateRequest(
        apiType: _apiType,
        method: options.method,
        path: options.path,
        baseUrl: _baseUrl,
        queryParams: options.queryParameters.isNotEmpty ? options.queryParameters : null,
        data: options.data,
        accessToken: authToken, // Forward the extracted auth token
        headers: customHeaders.isNotEmpty ? customHeaders : null, // Forward all headers
      );
      
      print('🔄 IsolateHttpClientAdapter - Sending request: ${options.method} ${options.path}');
      
      // Send request through NetworkIsolate
      final response = await _networkIsolate.sendRequest(request);
      
      print('🔍 IsolateHttpClientAdapter - Raw response: $response');
      print('🔍 IsolateHttpClientAdapter - Response type: ${response.runtimeType}');
      
      // EXTENDED DEBUGGING: Analyze the response structure in detail
      if (response is Map) {
        print('🔬 DEBUG - Map response keys: ${response.keys.toList()}');
        
        // Check for nested data property
        if (response.containsKey('data')) {
          print('🔬 DEBUG - Found nested "data" property!');
          print('🔬 DEBUG - Data type: ${response['data'].runtimeType}');
          if (response['data'] is Map) {
            print('🔬 DEBUG - Data keys: ${(response['data'] as Map).keys.toList()}');
          }
        }
        
        // Check for common response properties
        if (response.containsKey('statusCode')) {
          print('🔬 DEBUG - Response contains statusCode: ${response['statusCode']}');
        }
      }
      
      // Extract status code
      final statusCode = response is Map && response.containsKey('statusCode')
          ? response['statusCode'] as int
          : 200;
      
      // Create headers map
      final headers = <String, List<String>>{
        Headers.contentTypeHeader: [Headers.jsonContentType],
      };
      
      // CRITICAL FIX: Understanding the Retrofit parsing system:
      // 1. Retrofit's generated code expects to receive a raw response that matches the DTO structure
      // 2. Retrofit converter will apply the fromJson method to whatever we provide here
      // 3. The generated fromJson method expects the top-level object to have fields matching DTO fields
      // 4. Adding/removing wrappers causes the NoSuchMethodError: '.data' error

      // Create the response string - DIRECTLY MATCHING what Retrofit expects
      String responseString;
      dynamic retrofitData;
      
      // CRITICAL FIX: Special handling for nested data property to avoid NoSuchMethodError
      // If we have a map with a single 'data' property, and that contains our actual response data,
      // extract the inner data to match what Retrofit expects
      if (response is Map && 
          response.containsKey('data') && 
          response.length == 1) {
        print('🔄 TRANSFORM - Found data wrapper, extracting inner content for Retrofit');
        retrofitData = response['data'];
        responseString = jsonEncode(retrofitData);
        print('🔄 TRANSFORM - Extracted data type: ${retrofitData.runtimeType}');
      }
      // If response is already a string, use it directly
      else if (response is String) {
        responseString = response;
        retrofitData = jsonDecode(response); // Parse it to inspect
        print('📦 IsolateHttpClientAdapter - Using raw string response');
      } 
      // RETROFIT COMPATIBILITY: Map direct to JSON without wrapping/unwrapping
      else if (response is Map) {
        // This is critical: we need the map to be directly serialized as-is
        retrofitData = response;
        responseString = jsonEncode(response);
        print('📦 IsolateHttpClientAdapter - Encoded map response directly');
      }
      // Lists are fine as-is too
      else if (response is List) {
        retrofitData = response;
        responseString = jsonEncode(response);
        print('📦 IsolateHttpClientAdapter - Encoded list response');
      }
      // For everything else, do our best to convert to a string
      else {
        responseString = response.toString();
        try {
          retrofitData = jsonDecode(responseString);
        } catch (e) {
          retrofitData = response;
        }
        print('📦 IsolateHttpClientAdapter - Converting other type: ${response.runtimeType}');
      }
      
      print('📦 IsolateHttpClientAdapter - Final response: ${responseString.length > 200 ? responseString.substring(0, 200) + "..." : responseString}');
      
      // Validate the final structure will work with Retrofit
      if (retrofitData is Map) {
        if (options.path.contains('/auth/salesforce/obtain-token')) {
          print('🧪 RETROFIT - Checking structure for SalesforceTokenResponse');
          // Check expected fields based on the endpoint
          if (retrofitData.containsKey('accessToken')) {
            print('✅ RETROFIT - Found accessToken field, structure looks good for SalesforceTokenResponse');
          } else {
            print('⚠️ RETROFIT - Missing accessToken field for SalesforceTokenResponse!');
          }
        } else if (options.path.contains('/organizations/') && options.path.contains('/sessions')) {
          print('🧪 RETROFIT - Checking structure for SessionsResponse');
          if (retrofitData.containsKey('sessionToken')) {
            print('✅ RETROFIT - Found sessionToken field, structure looks good for SessionsResponse');
          } else {
            print('⚠️ RETROFIT - Missing sessionToken field for SessionsResponse!');
          }
        }
      }
      
      // FOR DEBUGGING: Validate JSON and print the structure
      try {
        final decoded = jsonDecode(responseString);
        print('📦 IsolateHttpClientAdapter - Valid JSON: ${decoded.runtimeType}');
        if (decoded is Map) {
          print('📦 IsolateHttpClientAdapter - JSON keys: ${decoded.keys.toList()}');
        }
      } catch (e) {
        print('⚠️ IsolateHttpClientAdapter - Invalid JSON: $e');
      }
      
      return ResponseBody.fromString(
        responseString,
        statusCode,
        headers: headers,
      );
    } catch (e) {
      print('❌ IsolateHttpClientAdapter - Error sending request: $e');
      rethrow;
    }
  }

  @override
  void close({bool force = false}) {}
}

/// Factory service for creating and managing network isolate and API clients
@singleton
class NetworkServiceFactory {
  // Private constructor for injectable
  NetworkServiceFactory._();
  
  /// Asynchronous factory method for injectable to use
  /// Create a NetworkServiceFactory instance with optionally specifying if this is a notification-triggered cold start
  /// [isNotificationTriggeredStart] - Set to true if the app was launched from a notification after being completely closed
  @factoryMethod
  static Future<NetworkServiceFactory> create({bool isNotificationTriggeredStart = false}) async {
    print('🔧 NetworkServiceFactory.create() - Starting...');
    
    try {
      print('🔧 NetworkServiceFactory.create() - Creating instance...');
      final instance = NetworkServiceFactory._();
      print('🔧 NetworkServiceFactory.create() - Instance created, calling initialize...');
      
      await instance.initialize(
        isNotificationTriggeredStart: isNotificationTriggeredStart,
      );
      
      print('✅ NetworkServiceFactory.create() - Initialize completed successfully');
      return instance;
    } catch (e, stackTrace) {
      print('❌ NetworkServiceFactory.create() - CRITICAL ERROR: $e');
      print('📍 NetworkServiceFactory.create() - Stack trace: $stackTrace');
      rethrow;
    }
  }
  
  // Shared isolate instance
  NetworkIsolate? _networkIsolate;
  
  // API clients
  ShimServiceApi? _shimServiceApi;
  SalesforceApi? _salesforceApiClient;
  
  // Dependencies
  LocalStorageRepository? _localStorageRepository;
  
  // Flag to track initialization
  bool _isInitialized = false;
  
  // Token readiness tracking
  bool _areTokensReady = false;
  Completer<void>? _tokenReadinessCompleter;
  
  // Flag to track if this is a notification-triggered cold start
  bool _isNotificationTriggeredStart = false;
  
  /// Helper method to mask token values for logging
  String _maskToken(String? token) {
    if (token == null) return 'null';
    if (token.isEmpty) return 'empty';
    if (token.length <= 8) return '****';
    
    // Show first 4 and last 4 characters, mask everything in between
    return '${token.substring(0, 4)}...${token.substring(token.length - 4)}';
  }
  
  /// Initialize the network service factory
  /// Both parameters are optional to allow initialization during DI setup
  Future<void> initialize({
    LoggingUseCase? loggingUseCase,
    LocalStorageRepository? localStorageRepository,
    bool isNotificationTriggeredStart = false,
  }) async {
    if (_isInitialized) return;

    print('🏭 NetworkServiceFactory - Starting initialize()... isNotificationTriggeredStart: $isNotificationTriggeredStart');

    // Set notification triggered flag if specified
    _isNotificationTriggeredStart = isNotificationTriggeredStart;

    // Initialize token readiness tracking
    _areTokensReady = false;
    _tokenReadinessCompleter = Completer<void>();

    // Store repositories for later use
    _localStorageRepository = localStorageRepository;

    try {
      // Create network isolate with logging enabled
      print('🏭 NetworkServiceFactory - Creating NetworkIsolate instance...');
      _networkIsolate = NetworkIsolate.getInstance(
        enableLogging: true, // Enable logging by default
        requestTimeout: const Duration(seconds: 30),
      );

      // CRITICAL FIX: Initialize the network isolate asynchronously to prevent blocking main thread
      print('🏭 NetworkServiceFactory - Starting NetworkIsolate.initialize() asynchronously...');

      // Don't await the initialization - let it happen in the background
      // This prevents blocking the main thread during app startup
      _networkIsolate!.initialize().then((_) {
        print('✅ NetworkServiceFactory - NetworkIsolate.initialize() completed successfully in background');
      }).catchError((e, stackTrace) {
        print('❌ NetworkServiceFactory - NetworkIsolate initialization failed in background: $e');
        print('📍 NetworkServiceFactory - Stack trace: $stackTrace');
        // Don't set _networkIsolate to null here as it might be used by other parts
        // Instead, let individual requests handle the failure
      });

      print('✅ NetworkServiceFactory - NetworkIsolate initialization started in background');

    } catch (e, stackTrace) {
      print('❌ NetworkServiceFactory - CRITICAL ERROR during NetworkIsolate setup: $e');
      print('📍 NetworkServiceFactory - Stack trace: $stackTrace');

      // Set a flag or throw to prevent further usage
      _networkIsolate = null;
      throw Exception('NetworkIsolate setup failed: $e');
    }
    
    // IMPORTANT: Set base URLs explicitly early in initialization
    // These hardcoded URLs ensure the isolate has base URLs before any requests
    print('🏭 NetworkServiceFactory - Setting default base URLs in network isolate');
    final defaultBaseUrls = <ApiType, String>{
      ApiType.salesforce: 'https://login.salesforce.com',
      ApiType.shimService: 'https://shim-service-prod.herokuapp.com',
    };
    _networkIsolate!.updateBaseUrls(defaultBaseUrls);
    
    // Get current auth tokens from storage and update isolate if possible
    if (localStorageRepository != null) {
      try {
        print('🏭 NetworkServiceFactory - Getting credentials from local storage');
        final credentials = await localStorageRepository.getCredentials();
        
        // Update tokens in the isolate
        print('🏭 NetworkServiceFactory - Updating auth tokens in network isolate');
        
        // Check if we have valid tokens
        final hasAuthToken = credentials.authorizationToken != null && 
                            credentials.authorizationToken!.isNotEmpty;
        final hasAccessToken = credentials.accessToken != null && 
                              credentials.accessToken!.isNotEmpty;
        
        print('🏭 NetworkServiceFactory - Token status: hasAuthToken=$hasAuthToken, hasAccessToken=$hasAccessToken');
        
        // Update tokens in the isolate
        _networkIsolate!.updateAuthTokens(
          authorizationToken: credentials.authorizationToken, // For Shim Service API
          accessToken: credentials.sessionToken,            // For Salesforce API
        );
        
        // Mark tokens as ready if they exist
        if ((hasAuthToken || hasAccessToken) && !_areTokensReady) {
          _areTokensReady = true;
          if (!_tokenReadinessCompleter!.isCompleted) {
            print('✅ NetworkServiceFactory - Tokens are available, marking as ready');
            _tokenReadinessCompleter!.complete();
          }
        }
        
        // Update base URLs if available in credentials
        if (credentials.instanceUrl != null && credentials.instanceUrl!.isNotEmpty) {
          print('🏭 NetworkServiceFactory - Updating Salesforce base URL to: ${credentials.instanceUrl}');
          final updatedBaseUrls = <ApiType, String>{
            ApiType.salesforce: credentials.instanceUrl!,
          };
          _networkIsolate!.updateBaseUrls(updatedBaseUrls);
        }
      } catch (e) {
        print('⚠️ NetworkServiceFactory - Unable to update auth tokens in network isolate: $e');
        
        // If this is a notification-triggered start, we must ensure tokens are eventually ready
        // to prevent ANRs, even if we need to retry
        if (_isNotificationTriggeredStart && !_areTokensReady) {
          // Schedule a retry after a short delay
          print('🔄 NetworkServiceFactory - Scheduling token loading retry for notification start');
          Future.delayed(const Duration(milliseconds: 500), () async {
            if (_localStorageRepository != null && !_areTokensReady) {
              try {
                final credentials = await _localStorageRepository!.getCredentials();
                _networkIsolate!.updateAuthTokens(
                  authorizationToken: credentials.authorizationToken,
                  accessToken: credentials.sessionToken,
                );
                
                // Complete the token readiness completer
                if (!_tokenReadinessCompleter!.isCompleted) {
                  print('✅ NetworkServiceFactory - Tokens loaded in retry, marking as ready');
                  _areTokensReady = true;
                  _tokenReadinessCompleter!.complete();
                }
              } catch (retryError) {
                print('❌ NetworkServiceFactory - Token retry also failed: $retryError');
                // Even if retry fails, we need to unblock the app after a timeout
                if (!_tokenReadinessCompleter!.isCompleted) {
                  print('⚠️ NetworkServiceFactory - Token loading failed, unblocking requests anyway');
                  _tokenReadinessCompleter!.complete();
                }
              }
            }
          });
        } else if (!_tokenReadinessCompleter!.isCompleted) {
          // If not a notification start, still complete to prevent blocking
          print('⚠️ NetworkServiceFactory - Normal start with token failure, unblocking requests');
          _tokenReadinessCompleter!.complete();
        }
      }
    } else {
      // No local storage repository available, so we can't load tokens
      // Complete the token readiness completer to avoid blocking requests
      if (!_tokenReadinessCompleter!.isCompleted) {
        print('⚠️ NetworkServiceFactory - No LocalStorageRepository available, unblocking requests');
        _tokenReadinessCompleter!.complete();
      }
    }
    
    print('✅ NetworkServiceFactory - Initialization complete');
    _isInitialized = true;
  }
  
  /// Get or create ShimServiceApi client - uses the retrofit-generated implementation
  ShimServiceApi getShimServiceApi(String baseUrl) {
    _assertInitialized();

    // Ensure network isolate is initialized before creating clients
    _ensureNetworkIsolateReady();

    // Ensure tokens are ready before creating clients
    // This is especially important for notification-triggered cold starts
    if (_isNotificationTriggeredStart && !_areTokensReady && _tokenReadinessCompleter != null) {
      print('⏳ NetworkServiceFactory - Waiting for tokens to be ready before creating ShimServiceApi');
      // We don't await here as this would block the call - the network isolate will handle the actual waiting
    }

    print('🏭 NetworkServiceFactory - getShimServiceApi called with baseUrl: "$baseUrl"');

    // Check if client already exists
    if (_shimServiceApi != null) {
      print('⚠️ NetworkServiceFactory - Using existing ShimServiceApi instead of creating new one with baseUrl: "$baseUrl"');
    } else {
      print('🔨 NetworkServiceFactory - Creating new ShimServiceApi with baseUrl: "$baseUrl"');

      // Create a Dio instance that will use the NetworkIsolate
      final dio = _createIsolateDio(ApiType.shimService, baseUrl);

      // Create the retrofit-generated ShimServiceApi
      _shimServiceApi = ShimServiceApi(dio, baseUrl: baseUrl);
    }

    return _shimServiceApi!;
  }

  /// Ensure network isolate is ready for use, with lazy initialization if needed
  void _ensureNetworkIsolateReady() {
    if (_networkIsolate == null) {
      print('⚠️ NetworkServiceFactory - NetworkIsolate is null, creating new instance');
      _networkIsolate = NetworkIsolate.getInstance(
        enableLogging: true,
        requestTimeout: const Duration(seconds: 30),
      );

      // Initialize asynchronously
      _networkIsolate!.initialize().catchError((e) {
        print('❌ NetworkServiceFactory - Lazy NetworkIsolate initialization failed: $e');
      });
    }
  }
  
  /// Creates a Dio instance that delegates all HTTP requests to the NetworkIsolate
  Dio _createIsolateDio(ApiType apiType, String baseUrl) {
    final dio = Dio();
    
    // Set the base URL
    dio.options.baseUrl = baseUrl;
    
    // Add token readiness interceptor for notification-triggered cold starts
    dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // If this is a notification-triggered start and tokens aren't ready yet,
        // wait for tokens to be loaded before proceeding with the request
        if (_isNotificationTriggeredStart && !_areTokensReady && _tokenReadinessCompleter != null) {
          try {
            print('⏳ NetworkServiceFactory - Waiting for tokens to be ready before request: ${options.path}');
            // Wait with a timeout to avoid ANR
            await _tokenReadinessCompleter!.future.timeout(
              const Duration(seconds: 5),
              onTimeout: () {
                print('⚠️ NetworkServiceFactory - Token readiness timeout reached, proceeding anyway');
                // We need to complete the completer to unblock other requests
                if (!_tokenReadinessCompleter!.isCompleted) {
                  _tokenReadinessCompleter!.complete();
                }
              },
            );
            print('✅ NetworkServiceFactory - Tokens ready, proceeding with request: ${options.path}');
          } catch (e) {
            print('❌ NetworkServiceFactory - Error waiting for tokens: $e');
            // If there's an error waiting for tokens, still proceed with the request
            // to avoid blocking the app, but it will likely fail with a 401
          }
        }
        return handler.next(options);
      },
    ));
    
    // Pretty logging of requests and responses in debug mode
    dio.interceptors.add(PrettyDioLogger(
      requestHeader: true,
      requestBody: true,
      responseBody: true,
      responseHeader: true,
      error: true,
      compact: false,
      logPrint: (object) => print('🔵 [SHIM_SERVICE_PRETTY_DIO] $object'),
    ));
    
    // Custom HttpClientAdapter that routes all requests through NetworkIsolate
    dio.httpClientAdapter = _IsolateHttpClientAdapter(_networkIsolate!, apiType, baseUrl);
    
    return dio;
  }
  
  /// Get or create SalesforceApi client
  SalesforceApi getSalesforceApi(String baseUrl) {
    _assertInitialized();

    // Ensure network isolate is initialized before creating clients
    _ensureNetworkIsolateReady();

    // Ensure tokens are ready before creating clients
    // This is especially important for notification-triggered cold starts
    if (_isNotificationTriggeredStart && !_areTokensReady && _tokenReadinessCompleter != null) {
      print('⏳ NetworkServiceFactory - Waiting for tokens to be ready before creating SalesforceApi');
      // We don't await here as this would block the call - the network isolate will handle the actual waiting
    }

    if (_salesforceApiClient == null) {
      final dio = _createIsolateDio(ApiType.salesforce, baseUrl);
      _salesforceApiClient = SalesforceApi(dio, baseUrl: baseUrl);
    }

    return _salesforceApiClient!;
  }
  
  /// Update authentication tokens in the network isolate
  Future<void> updateAuthTokens({
    String? authorizationToken,
    String? accessToken,
  }) async {
    _assertInitialized();
    
    print('🔐 NetworkServiceFactory - Updating auth tokens:');
    print('   - accessToken: ${_maskToken(accessToken)}');
    print('   - authorizationToken: ${_maskToken(authorizationToken)}');
    
    await _networkIsolate!.updateAuthTokens(
      authorizationToken: authorizationToken,  // For Shim Service API
      accessToken: accessToken                // For Salesforce API
    );
    
    // If we have a local storage repository, also update tokens there
    if (_localStorageRepository != null) {
      try {
        print('🔐 NetworkServiceFactory.updateAuthTokens - Also updating tokens in local storage');
        final credentials = await _localStorageRepository!.getCredentials();
        
        // Create updated credentials with new token values
        final updatedCredentials = credentials.copyWith(
          authorizationToken: authorizationToken ?? credentials.authorizationToken,
          sessionToken: accessToken ?? credentials.sessionToken,
        );
        
        // Save updated credentials back to storage
        // Use the method that actually exists in the repository
        await _localStorageRepository!.setCredentials(updatedCredentials);
        print('✅ NetworkServiceFactory.updateAuthTokens - Successfully updated tokens in local storage');
      } catch (e) {
        print('❌ NetworkServiceFactory.updateAuthTokens - Failed to update tokens in local storage: $e');
      }
    } else {
      print('⚠️ NetworkServiceFactory.updateAuthTokens - No localStorageRepository available to update tokens in storage');
    }
  }
  
  /// Check if network isolate is initialized
  void _assertInitialized() {
    if (!_isInitialized || _networkIsolate == null) {
      throw StateError('NetworkServiceFactory not initialized. '
          'Call initialize() before using any API clients.');
    }
  }
  
  /// Dispose network isolate and clients
  void dispose() {
    _networkIsolate?.dispose();
    _networkIsolate = null;
    _shimServiceApi = null;
    _salesforceApiClient = null;
    _isInitialized = false;
  }
}
